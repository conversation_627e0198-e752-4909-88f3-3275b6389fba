// v1.50以降、日本語を追加する場合は、
// lib/constants/lang/ja.tsに追加してください。
export const ja = {
    common: {
        required: '必須',
        placeholder: {
            input: '{text}を入力してください',
            halfWidthMinMaxLength: '半角英数字{min}～{max}文字',
        }
    },
    button: {
        add: '追加',
        edit: '編集',
        delete: '削除',
        save: '保存',
        cancel: 'キャンセル',
        up: '上へ',
        add_menu: 'メニュー追加',
    },
    
    validation : {
        notSelected: '{text}が未選択です',
        required: '{text}は必須項目です',
        maxLength: '最大文字数は{max}文字です',
        minLength: '{min}文字以内で入力してください',
        minMaxLength: '{text}は{min}文字以上{max}文字以内の半角英数字で入力してください',
        alreadyRegistered: '登録済みの{text}です',
    },
    dialog: {
        deleteConfirm: '削除すると元に戻せません。\n{text}を削除しますか？',
        default: 'デフォルト',
        upload:'アップロード',
    },
    alert: {
        created: '{text}が作成されました',
        delete: '{text}が削除されました',
        update: '{text}が更新されました',
        error: '{text}は削除できません',
        notSelected: '{text}が未選択です',
    },
    //  ユーザー制御権限管理
    userPermission: {
        userControlPermission: 'ユーザー制御権限',
        userControl: 'ユーザー制御権限管理',
        csvControlPermissionId: '制御権限ID',
        userControlPermissionName: 'ユーザー制御権限名',
        userControlPermissionSetting: 'ユーザー制御権限設定',
        memo: 'メモ',
        controlSetting: '制御設定',
        new: '追加',
        edit: '編集',
        update: '更新',
        permitLevel: 'ベース権限',
        table: {
            csvControlPermissionId: '制御権限ID',
            permitLevel: 'ベース権限',
            name: 'ユーザー制御権限名',
            updatedAt: '更新日時',
            menuName: 'メニュー名',
            detailMenuName: '詳細メニュー名',
            access: '閲覧',
            action: '編集',
            download: 'ダウンロード',
        },
        notFound: '制御権限IDが見つかりません',
        userControlPermissionMasterUpdate: 'ユーザー制御権限マスター更新',
    },
    //  私の担当一覧右サイドメニュー表示設定
    rightPanelWidgets: {
        rightPanelWidgetsSetting: '私の担当一覧 右サイドメニュー表示設定',
        edit: '編集',
        title: 'タイトル',
        icon: 'アイコン画像',
        default_image: 'デフォルト画像',
        uploaded_image: 'アップロード画像',
        url: '表示URL',
        title_placeholder: '推奨 全角 6文字',
        icon_placeholder: '推奨 w36/h36(px) png形式',
    },
    errorCodes:{
        107: 'APIエラー: 登録済みの{text}です',
        112: 'APIエラー: {text}が見つかりません',
        117: 'APIエラー: 利用中の{text}は削除できません',
        118: 'APIエラー: 登録可能な{text}は最大{max}個です',
        150: 'APIエラー: 不正なリクエストです',
    }
} as const;

export type JaType = typeof ja;