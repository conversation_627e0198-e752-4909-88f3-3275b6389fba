import React from 'react';
import { <PERSON>sedButton, Snackbar, TextField, Paper } from 'material-ui';
import ProgressOverlay from '../../../../common/ProgressOverlay';
import FormCss from '../../../../common/FormCss';
import KonnectPlusCCAPIActionCreator from '../../../../../actions/KonnectPlusCCAPIActionCreator';
import Konnect<PERSON><PERSON>ctionCreator from '../../../../../actions/KonnectAPIActionCreator';
import PermissionStore from '../../../../../stores/PermissionStore';
import CommonStyle from '../../../../themes/CommonStyle';
import IESelectField from '../../../../common/ieCompat/IESelectField';
import APIRoot from '../../../../../actions/APIRoot';
import { RightPanelWidgetsSettings } from '../../../../../stores/apiResults/RightPanelWidgetsSettings';
import { ja } from '../../../../../constants/lang/ja';

const PERMISSION_PATH = 'callcenterOperationMiscRightPanelWidgetsSetting';
const FIELD_KEY = {
  IFRAME_TITLE: 'iframeTitle',
  IFRAME_ICON: 'iframeIcon',
  IFRAME_URL: 'iframeUrl',
}

const IMAGE_TYPES = [
  'DEFAULT',
  'UPLOAD',
]

interface WidgetData {
  id: string;
  key: number;
  title: string;
  url: string;
  iconFileId: string;
  titleError?: string;
  urlError?: string;
  iconError?: string;
  _toDelete?: boolean; // 削除フラグ（保存時に使用）
}

interface State {
  editMode: boolean;
  snackMessage: string;
  isLoading: boolean;
  canAction: boolean;
  widgets: WidgetData[];
  // 画像アップロード関連のstate
  [key: string]: any; // 動的なキーをサポート
}

const MAX_WIDGETS = 5;

const styles = {
  InlineImage: {
    height: '48px',
    width: '48px',
    marginLeft: '10px',
    backgroundColor: 'rgba(0, 0, 0, .1)',
    fontSize: '10px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
}

export default class RightPanelWidgets extends React.Component<any, State> {
  static contextTypes = {
    i18n: React.PropTypes.object,
    router: React.PropTypes.func,
    auth_domainId: React.PropTypes.string,
    domainId: React.PropTypes.string,
    baseUrl: React.PropTypes.string,
  }

  timeoutToken: any = null;
  snackRef: any = null;
  inputFileStateHandlerTimer: any = null;
  [key: string]: any; // 動的なrefをサポート

  constructor(props) {
    super(props);
    this._onPermissionChange = this._onPermissionChange.bind(this);
    this._onStoreChange = this._onStoreChange.bind(this);
    this.inputFileStateHandler = this.inputFileStateHandler.bind(this);
    
    this.state = {
      editMode: false,
      snackMessage: '',
      isLoading: false,
      canAction: PermissionStore.canAction(PERMISSION_PATH),
      widgets: [{
        id: '',
        key: 0,
        title: '',
        url: '',
        iconFileId: '',
        titleError: '',
        urlError: '',
        iconError: '',
      }],
      // 初期ウィジェットの画像state
      image_header_icon_0Type: 'DEFAULT',
      image_header_icon_0PreviousType: null,
      image_header_icon_0RefreshTime: Date.now(),
      inputFileStateHandler: null,
      processingPublicFileName: '',
      progress: false,
    };
  }

  componentDidMount() {
    PermissionStore.addChangeListener(this._onPermissionChange);
    RightPanelWidgetsSettings.addChangeListener(this._onStoreChange);
    this.loadInitialData();
    window.addEventListener('focus', this.inputFileStateHandler);
  }

  componentDidUpdate() {
    if (this.state.snackMessage) {
      this.snackRef?.show();
      if (!this.timeoutToken) {
        this.timeoutToken = setTimeout(() => {
          this.snackRef?._bindClickAway();
          this.timeoutToken = null;
        }, 100);
      }
    }
  }

  componentWillUnmount() {
    PermissionStore.removeChangeListener(this._onPermissionChange);
    RightPanelWidgetsSettings.removeChangeListener(this._onStoreChange);
    window.removeEventListener('focus', this.inputFileStateHandler);
    if (this.timeoutToken) {
      clearTimeout(this.timeoutToken);
      this.timeoutToken = null;
    }
    if (this.inputFileStateHandlerTimer) {
      clearTimeout(this.inputFileStateHandlerTimer);
      this.inputFileStateHandlerTimer = null;
    }
  }

  _onPermissionChange() {
    this.setState({
      canAction: PermissionStore.canAction(PERMISSION_PATH)
    });
  }

  _onStoreChange() {
    const storeData = RightPanelWidgetsSettings.data();
    
    // POST成功時の処理（他のファイルのパターンを参考）
    if (storeData && storeData.success) {
      // POST成功後、最新のデータを取得してから成功メッセージを表示
      this.setState({
        editMode: false,
        snackMessage: this.context.i18n?.admin_domain_update_successful || '更新が完了しました',
      });
      // 成功時にも最新データを取得（削除処理完了を待つために少し遅延）
      setTimeout(() => {
        KonnectPlusCCAPIActionCreator.getRightPanelWidgetsSettings();
      }, 500);
      return;
    }

    // エラー時の処理
    if (storeData && storeData.error && storeData.error !== 0) {
      // 認証エラー（error: 200）の場合は自動でログアウト処理されるため、メッセージは表示しない
      if (storeData.error !== 200) {
        this.setState({
          isLoading: false,
          snackMessage: 'サーバーエラーが発生しました',
        });
      } else {
        this.setState({
          isLoading: false,
        });
      }
      return;
    }
    
    if (storeData && storeData.widgets && Array.isArray(storeData.widgets)) {
      const widgets = storeData.widgets.length > 0 
        ? storeData.widgets.map((widget, index) => ({
            key: index,
            id: widget.id || '',
            title: widget.title || '',
            url: widget.url || '',
            iconFileId: widget.iconFileId || '',
            titleError: '',
            urlError: '',
            iconError: '',
          }))
        : [{
            id: '',
            key: 0,
            title: '',
            url: '',
            iconFileId: '',
            titleError: '',
            urlError: '',
            iconError: '',
          }];

      // 画像stateの初期化
      const imageStates = {};
      widgets.forEach((widget, index) => {
        const serverWidget = storeData.widgets[index];
        const key = `image_header_icon_${widget.key}`;
        // サーバーからのiconTypeまたはiconFileIdの存在で判定
        const isUploaded = serverWidget?.iconType === 'uploaded' || serverWidget?.iconFileId;
        imageStates[`${key}Type`] = isUploaded ? 'UPLOAD' : 'DEFAULT';
        // RefreshTimeは既存の値を保持し、不要な更新を避ける
        if (!this.state[`${key}RefreshTime`]) {
          imageStates[`${key}RefreshTime`] = Date.now();
        }
        imageStates[`${key}PreviousType`] = null;
      });

      this.setState({
        widgets,
        ...imageStates,
        isLoading: false,
      });
    } else {
      this.setState({
        widgets: [{
          id: '',
          key: 0,
          title: '',
          url: '',
          iconFileId: '',
          titleError: '',
          urlError: '',
          iconError: '',
        }],
        image_header_icon_0Type: 'DEFAULT',
        image_header_icon_0RefreshTime: Date.now(),
        image_header_icon_0PreviousType: null,
        isLoading: false,
      });
    }
  }

  inputFileStateHandler() {
    const handler = this.state.inputFileStateHandler;
    if (typeof handler === 'function') {
      clearTimeout(this.inputFileStateHandlerTimer);
      this.inputFileStateHandlerTimer = setTimeout(handler, 500);
    }
  }

  loadInitialData() {
    this.setState({ isLoading: true });
    KonnectPlusCCAPIActionCreator.getRightPanelWidgetsSettings();
  }

  onModeChange() {
    if (this.state.editMode) {
      this.loadInitialData();
    } else {
      // 編集モードに入る時にエラーメッセージをクリア
      const clearedWidgets = this.state.widgets.map(widget => ({
        ...widget,
        titleError: '',
        urlError: '',
        iconError: '',
      }));
      this.setState({ widgets: clearedWidgets });
    }
    this.setState({
      editMode: !this.state.editMode
    });
  }

  validation(key: string, value: string) {
    const error = {};
    error[key] = '';

    switch (key) {
      case FIELD_KEY.IFRAME_TITLE:
        if (!value) {
          error[key] = this.context.i18n?.error_common_required || '必須項目です';
        } else if (value.length > 20) {
          error[key] = 'タイトルは20文字以内で入力してください。';
        }
        break;
      case FIELD_KEY.IFRAME_URL:
        if (!value) {
          error[key] = this.context.i18n?.error_common_required || '必須項目です';
        } else if (!value.startsWith('https://')) {
          error[key] = 'httpsから始まるURLを入力してください。';
        } else if (!/^[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+$/.test(value)) {
          error[key] = '半角英数字で入力してください。';
        } else if (value.length > 2000) {
          error[key] = 'URLは2000文字以内で入力してください。';
        }
        break;
      case FIELD_KEY.IFRAME_ICON:
      default:
        break;
    }
    return error;
  }

  validateIcon(widget: WidgetData): string {
    const key = `image_header_icon_${widget.key}`;
    const imageType = this.state[`${key}Type`];
    
    if (imageType === 'UPLOAD' && !widget.iconFileId) {
      return this.context.i18n?.error_common_required || '必須項目です';
    }
    
    return '';
  }

  validationAll() {
    let hasError = false;
    const updatedWidgets = this.state.widgets.map(widget => {
      const titleError = this.validation(FIELD_KEY.IFRAME_TITLE, widget.title);
      const urlError = this.validation(FIELD_KEY.IFRAME_URL, widget.url);
      const iconError = this.validateIcon(widget);

      if (titleError[FIELD_KEY.IFRAME_TITLE] || iconError || urlError[FIELD_KEY.IFRAME_URL]) {
        hasError = true;
      }

      return {
        ...widget,
        titleError: titleError[FIELD_KEY.IFRAME_TITLE],
        iconError: iconError,
        urlError: urlError[FIELD_KEY.IFRAME_URL],
      };
    });

    this.setState({ widgets: updatedWidgets });
    return hasError;
  }

  onWidgetFieldChange = (key: number, field: string, value: string) => {
    const updatedWidgets = this.state.widgets.map(widget => {
      if (widget.key === key) {
        const updatedWidget = { ...widget, [field]: value };
        
        return updatedWidget;
      }
      return widget;
    });

    // 保存ボタンの状態更新のためにstateを更新
    this.setState({ widgets: updatedWidgets });
  }

  onFieldBlur = (key: number, field: string) => {
    // フォーカスアウト時のバリデーションを無効化
    // 保存時のみバリデーションを実行
  }

  handleFileUpload(index: number, file: File) {
    const widget = this.state.widgets[index];
    if (!widget) return;

    const key = `image_header_icon_${widget.key}`;
    // 10桁のランダム英数字を生成
    const randomId = Math.random().toString(36).substring(2, 12).toLowerCase();
    const fileName = `rightPanelWidgets/${randomId}`;
    
    this.setState({
      processingPublicFileName: fileName,
    }, () => {
      const contentType = file.type || 'image/png';
      
      KonnectAPIActionCreator.postPublicFile(
        this.context.domainId,
        file,
        fileName,
        contentType,
        true/*exposeToAll*/,
        true/*noNeedAuth*/,
        false/*isAnonymous*/
      );
      
      // アップロード後の処理をシミュレート
      setTimeout(() => {
        // サーバーが期待する形式: domainId/rightPanelWidgets/10桁の英数字
        const iconFileId = `${this.context.domainId}/${fileName}`;
        this.onWidgetFieldChange(widget.key, 'iconFileId', iconFileId);
        
        // リフレッシュタイムを更新
        this.setState({
          [`${key}RefreshTime`]: Date.now(),
          processingPublicFileName: '',
          progress: false,
        });
      }, 1000);
    });
  }

  handleAdd() {
    if (this.state.widgets.length < MAX_WIDGETS) {
      const widgets = this.state.widgets;
      const newKey = widgets.length > 0 ? Math.max(...widgets.map(w => w.key)) + 1 : 0;
      
      const newWidget: WidgetData = {
        key: newKey,
        id: '',
        title: '',
        url: '',
        iconFileId: '',
        titleError: '',
        urlError: '',
        iconError: '',
      };
      
      // 新しいウィジェット用の画像state初期化
      const imageKey = `image_header_icon_${newKey}`;
      const newImageState = {
        [`${imageKey}Type`]: 'DEFAULT',
        [`${imageKey}PreviousType`]: null,
        [`${imageKey}RefreshTime`]: Date.now(),
      };
      
      const updatedWidgets = [...widgets, newWidget];
      this.setState({ 
        widgets: updatedWidgets,
        ...newImageState
      });
    }
  }

  handleRemove(index: number) {
    const widgets = this.state.widgets;
    const widget = widgets[index];
    
    if (widgets.length <= 1) {
      // 1つしかない場合
      if (widget.id) {
        // サーバー上のデータの場合は削除マークを付ける（元のデータを保持）
        const deletedWidget: WidgetData = {
          ...widget, // 元のデータを保持
          titleError: '',
          urlError: '',
          iconError: '',
          _toDelete: true, // 削除フラグを追加
        };
        
        // 削除時に画像状態はクリアしない（APIで元の値が必要）
        this.setState({ 
          widgets: [deletedWidget],
        });
      } else {
        // 新規作成データの場合は単純にリセット
        const resetWidget: WidgetData = {
          key: 0,
          id: '',
          title: '',
          url: '',
          iconFileId: '',
          titleError: '',
          urlError: '',
          iconError: '',
        };
        
        const imageKey = 'image_header_icon_0';
        this.setState({ 
          widgets: [resetWidget],
          [`${imageKey}Type`]: 'DEFAULT',
          [`${imageKey}PreviousType`]: null,
          [`${imageKey}RefreshTime`]: Date.now(),
        });
      }
    } else {
      if (widget.id) {
        // サーバー上のデータの場合は削除マークを付ける（元のデータを保持）
        const updatedWidgets = widgets.map((w, i) => {
          if (i === index) {
            return { ...w, _toDelete: true };
          }
          return w;
        });
        
        this.setState({ widgets: updatedWidgets });
      } else {
        // 新規作成データの場合は単純に配列から削除
        const filteredWidgets = widgets.filter((_, i) => i !== index);
        
        // 削除されるウィジェットの画像stateも削除
        const imageKey = `image_header_icon_${widget.key}`;
        const newState = { widgets: filteredWidgets };
        delete newState[`${imageKey}Type`];
        delete newState[`${imageKey}PreviousType`];
        delete newState[`${imageKey}RefreshTime`];
        
        this.setState(newState);
      }
    }
  }

  handleMoveUp(index: number) {
    if (index > 0) {
      const widgets = [...this.state.widgets];
      
      // 削除対象でないウィジェットのみを取得
      const activeWidgets = widgets.filter(widget => !widget._toDelete);
      const activeIndex = activeWidgets.findIndex(widget => widget.key === widgets[index].key);
      
      if (activeIndex > 0) {
        // 実際のwidgets配列での位置を特定
        const currentWidgetKey = activeWidgets[activeIndex].key;
        const previousWidgetKey = activeWidgets[activeIndex - 1].key;
        
        const currentIndex = widgets.findIndex(w => w.key === currentWidgetKey);
        const previousIndex = widgets.findIndex(w => w.key === previousWidgetKey);
        
        // 入れ替え
        [widgets[currentIndex], widgets[previousIndex]] = [widgets[previousIndex], widgets[currentIndex]];
        
        this.setState({ widgets });
      }
    }
  }

  handleSave() {
    if (this.validationAll()) {
      this.setState({
        snackMessage: this.context.i18n?.error_common_has_field_error || '入力エラーがあります'
      });
      return;
    }

    // 削除対象のウィジェット（既存データで_toDeleteフラグがあるもの）
    const deleteWidgets = this.state.widgets
      .filter(widget => {
        const hasId = !!widget.id;
        const hasDeleteFlag = !!widget._toDelete;
        return hasId && hasDeleteFlag;
      })
      .map((widget, index) => {
        const deleteData: any = {
          id: widget.id,
          operation: 'delete' as const,
          order: index + 1, // 削除の順番も設定
          title: widget.title, // 元のタイトルを保持
          url: widget.url, // 元のURLを保持
          iconType: widget.iconFileId ? 'uploaded' : 'default',
        };
        
        // iconFileIdがある場合は含める
        if (widget.iconFileId) {
          deleteData.iconFileId = widget.iconFileId;
        }
        
        return deleteData;
      });

    // 有効なウィジェット（空でなく、削除対象でないもの）
    const validWidgets = this.state.widgets.filter(widget => 
      widget.title.trim() && widget.url.trim() && !widget._toDelete
    );

    const updateInsertWidgets = validWidgets.map((widget, index) => {
      const baseWidget: any = {
        operation: widget.id ? 'update' : 'insert',
        order: index + 1, // 1から始まる
        title: widget.title.trim(),
        url: widget.url.trim(),
        iconType: this.state[`image_header_icon_${widget.key}Type`] === 'UPLOAD' ? 'uploaded' : 'default',
      };

      // 更新の場合のみidを追加
      if (widget.id) {
        baseWidget.id = widget.id;
      }

      // アップロード画像の場合のみiconFileIdを追加
      if (this.state[`image_header_icon_${widget.key}Type`] === 'UPLOAD' && widget.iconFileId) {
        baseWidget.iconFileId = widget.iconFileId;
      }

      return baseWidget;
    });

    // 削除操作と更新/追加操作を結合（削除を後に配置）
    const widgets = [...updateInsertWidgets, ...deleteWidgets];
    
    // サーバーは配列を直接期待している
    const params = widgets;
    
    this.setState({ isLoading: true });

    // 他のファイルのパターンに合わせて、POST成功後のデータ再取得は_onStoreChangeで処理
    KonnectPlusCCAPIActionCreator.postRightPanelWidgetsSettings(params);
  }

  hasNoError() {
    // 編集モードでない場合は常にfalse（保存ボタンを非活性）
    if (!this.state.editMode) return false;
    
    // 必須項目（タイトル、URL）が入力されているかチェック
    return this.state.widgets.every(widget => {
      // 空のウィジェットは除外（タイトルとURLが両方空の場合）
      if (!widget.title.trim() && !widget.url.trim()) {
        return true;
      }
      
      // どちらか一方でも入力されている場合は、両方必須
      if (widget.title.trim() || widget.url.trim()) {
        const titleValid = widget.title.trim().length > 0 && widget.title.length <= 20;
        const urlValid = widget.url.trim().length > 0 && 
                        widget.url.startsWith('https://') && 
                        widget.url.length <= 2000;
        return titleValid && urlValid;
      }
      
      return true;
    });
  }

  getErrorText(widget: WidgetData, key: string) {
    if (!this.state.editMode) return '';
    
    switch (key) {
      case FIELD_KEY.IFRAME_TITLE:
        return widget.titleError || '';
      case FIELD_KEY.IFRAME_URL:
        return widget.urlError || '';
      case FIELD_KEY.IFRAME_ICON:
        return widget.iconError || '';
      default:
        return '';
    }
  }

  onSnackDismiss() {
    this.setState({ snackMessage: '' });
  }

  handleCancel() {
    this.snackRef?.dismiss();
  }

  renderImageInput(widget: WidgetData, index: number) {
    const key = `image_header_icon_${widget.key}`;
    const refreshTimeKey = `${key}RefreshTime`;
    const refreshTime = this.state[refreshTimeKey] || Date.now();

    const menuItems = [
      { payload: IMAGE_TYPES[0], text: `${ja.dialog.default}` },
      { payload: IMAGE_TYPES[1], text: `${ja.dialog.upload}` },
    ];
    
    return (
      <div className='selectIconContainer' style={{display: 'flex', alignItems: 'center'}}>
        <div className='selectImageField'>
          <IESelectField
            labelStyle={{fontSize: '14px'}}
            menuItemStyle={{fontSize: '14px'}}
            selectedIndex={IMAGE_TYPES.indexOf(this.state[`${key}Type`] || 'DEFAULT')}
            menuItems={menuItems}
            onChange={this.onImageTypeChange(widget.key)}
            disabled={!this.state.editMode}
          />
          <input
            id={`hidden_fileInput_${key}`}
            ref={(ref) => {this[`hidden_fileInput_${key}Ref`] = ref}}
            style={{display: 'none'}}
            type="file"
            accept='image/*'
            onChange={(e) => this.handleFileUpload(index, e.target.files[0])}
          />
          <p style={{color: 'rgba(0, 0, 0, 0.5)',lineHeight: '1.2',fontSize: '12px', margin: '0'}}>
            {ja.rightPanelWidgets.icon_placeholder}
          </p>
        </div>
        {this.state[`${key}Type`] === 'DEFAULT' &&
          <Paper style={...styles.InlineImage}>
            <img
              src={`${APIRoot.getAssetsCdnURL()}/assets/images/admin/embedSample/img/image_avatar_guest.png`}
              alt={ja.rightPanelWidgets.default_image}
              style={{ width: '36px', height: '36px', objectFit: 'cover' }}
            />
          </Paper>
        }
        {this.state[`${key}Type`] === 'UPLOAD' &&
          <Paper style={styles.InlineImage} onClick={() => this.toggleFileInputDialog(widget.key)}>
            <img
              src={`${this.context.baseUrl}/api/globalPublicFiles/mst/${widget.iconFileId}?t=${refreshTime}`}
              alt={ja.rightPanelWidgets.uploaded_image}
              style={{ width: '36px', height: '36px', objectFit: 'cover' }}
            />
          </Paper>
        }
        {widget.iconError && 
          <p style={{ color: 'red', fontSize: '12px', margin: '5px 0' }}>
            {this.getErrorText(widget, FIELD_KEY.IFRAME_ICON)}
          </p>
        }
      </div>
    );
  }

  onImageTypeChange(widgetKey: number) {
    return (event, selectedIndex) => {
      const imageType = IMAGE_TYPES[selectedIndex];
      const key = `image_header_icon_${widgetKey}`;
      
      if (imageType === 'UPLOAD') {
        this.toggleFileInputDialog(widgetKey);
      } else {
        this.setState({
          [`${key}Type`]: imageType,
          [`${key}PreviousType`]: this.state[`${key}Type`],
        });
        
        this.onWidgetFieldChange(widgetKey, 'iconFileId', '');
      }
    }
  }

  toggleFileInputDialog(widgetKey: number) {
    const key = `image_header_icon_${widgetKey}`;
    const hiddenFileInput = (React as any).findDOMNode(this[`hidden_fileInput_${key}Ref`]);
    
    this.setState({
      [`${key}Type`]: 'UPLOAD',
      [`${key}PreviousType`]: this.state[`${key}Type`],
      inputFileStateHandler: () => {
        if (!hiddenFileInput.value) {
          this.setState({
            [`${key}Type`]: this.state[`${key}PreviousType`],
            [`${key}PreviousType`]: null,
            inputFileStateHandler: null,
            progress: false,
          });
        }
      },
      progress: true,
    }, () => {
      hiddenFileInput.click();
    });
  }

  renderWidget(widget: WidgetData, index: number) {
    if (widget._toDelete) {
      return null;
    }
    
    return (
      <div key={widget.key} style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '40px'}} className='formWidgetArea'>
        <div style={{ flex: 1 }}>
          <div className='fieldRow' style={{ marginBottom: '20px' }}>
            <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.title}</p>
            <div>
              <TextField
                style={{ width: '600px' }}
                value={widget.title}
                disabled={!this.state.editMode}
                errorText={this.getErrorText(widget, FIELD_KEY.IFRAME_TITLE)}
                onChange={(e) => this.onWidgetFieldChange(widget.key, 'title', e.target.value)}
                onBlur={() => this.onFieldBlur(widget.key, 'title')}
              />
              <p style={{color: 'rgba(0, 0, 0, 0.5)',lineHeight: '1.2',fontSize: '12px', margin: '0'}}>
                {ja.rightPanelWidgets.title_placeholder}
              </p>
            </div>
          </div>
          
          <div className='fieldRow' style={{ marginBottom: '20px' }}>
            <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.icon}</p>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {this.renderImageInput(widget, index)}
            </div>
          </div>
          
          <div className='fieldRow' style={{ marginBottom: '20px' }}>
            <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.url}</p>
            <div>
              <TextField
                style={{ width: '600px' }}
                value={widget.url}
                placeholder='https://'
                disabled={!this.state.editMode}
                errorText={this.getErrorText(widget, FIELD_KEY.IFRAME_URL)}
                onChange={(e) => this.onWidgetFieldChange(widget.key, 'url', e.target.value)}
                onBlur={() => this.onFieldBlur(widget.key, 'url')}
              />
            </div>
          </div>
        </div>
        <div style={{ marginLeft: '20px', alignSelf: 'flex-end' }}>
          <RaisedButton
            label={ja.button.delete}
            backgroundColor={CommonStyle.warningButtonBgColor}
            labelColor={CommonStyle.warningButtonTextColor}
            onClick={() => this.handleRemove(index)}
            disabled={!this.state.editMode}
            style={{ marginRight: '10px' }}
          />
          <RaisedButton
            label={ja.button.up}
            onClick={() => this.handleMoveUp(index)}
            disabled={!this.state.editMode || (() => {
              const activeWidgets = this.state.widgets.filter(w => !w._toDelete);
              const activeIndex = activeWidgets.findIndex(w => w.key === widget.key);
              return activeIndex === 0;
            })()}
          />
        </div>
      </div>
    );
  }

  render() {
    if (this.state.isLoading) {
      return <ProgressOverlay timeout={500} />;
    }

    const { widgets } = this.state;
    const hasNoError = this.hasNoError();

    return (
      <div>
        <div style={FormCss.container}>
          <div style={FormCss.heading}>
            <div style={FormCss.headingTitle}>
              <h3>{ja.rightPanelWidgets.rightPanelWidgetsSetting}</h3>
            </div>
          </div>

          <div style={FormCss.content}>
            <div style={FormCss.subHeading}>
              <div style={FormCss.subHeadingTitle}>
                <h4>{ja.rightPanelWidgets.edit}</h4>
              </div>
              <div style={FormCss.rightButton}>
                <RaisedButton
                  label={this.state.editMode ? ja.button.cancel : ja.button.edit}
                  onClick={this.onModeChange.bind(this)}
                  disabled={!this.state.canAction}
                />
                <RaisedButton
                  label={ja.button.save}
                  secondary
                  onClick={this.handleSave.bind(this)}
                  disabled={!(this.state.editMode && hasNoError)}
                />
              </div>
            </div>

            <div className='formFieldArea'>
              {widgets.map((widget, index) => this.renderWidget(widget, index))}
              <div style={FormCss.rightButton}>
                <RaisedButton
                  label={ja.button.add_menu}
                  onClick={() => this.handleAdd()}
                  disabled={!this.state.editMode || widgets.filter(widget => !widget._toDelete).length >= MAX_WIDGETS}
                />
                <RaisedButton
                  label={ja.button.save}
                  secondary
                  onClick={this.handleSave.bind(this)}
                  disabled={!(this.state.editMode && hasNoError)}
                />
              </div>
            </div>
          </div>
        </div>

        <Snackbar
          style={{ zIndex: '10' }}
          ref={(ref) => { this.snackRef = ref }}
          message={this.state.snackMessage}
          onActionTouchTap={this.handleCancel.bind(this)}
          onDismiss={this.onSnackDismiss.bind(this)}
        />
      </div>
    );
  }
}
