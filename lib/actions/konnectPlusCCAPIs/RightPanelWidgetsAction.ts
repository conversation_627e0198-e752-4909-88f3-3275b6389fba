import request from 'superagent';
import CCAPIEvents from '../../constants/CC_APIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

export interface RightPanelWidgetRequest {
  id?: string;
  operation: 'insert' | 'update' | 'delete';
  order: number; // 削除操作でも必須
  title: string; // 削除操作でも必須
  url: string; // 削除操作でも必須
  iconType: string; // 削除操作でも必須
  iconFileId?: string;
}

const url = '/plusccapi/rightPanelWidgets';
const antiCsrfToken = Utils.getAntiCSRFToken();

const RightPanelWidgetsAction = {
  getRightPanelWidgetsSettings() {
    request
      .get(APIRoot.get() + url)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
            error: res?.error?.status || -1,
            data: { error: -1 },
          });
        } else {
          const error = res.body.error;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
              data: res.body,
            });
          } else {
            // 認証エラー（error: 200など）の場合はログアウト処理を実行
            if (error === 200) {
              Utils.logoutIfneccessary(new Error('Authentication failed'));
            }
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
              data: { error },
            });
          }
        }
      });
  },

  postRightPanelWidgetsSettings(params: RightPanelWidgetRequest[], cb: Function) {
    console.log('APIアクション送信パラメータ:', JSON.stringify(params, null, 2));
    
    request
      .post(APIRoot.get() + url)
      .withCredentials()
      .type('json')
      .send(params)
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        console.log('APIレスポンス - err:', err);
        console.log('APIレスポンス - res.body:', res?.body);
        console.log('APIレスポンス - res.error:', res?.error);
        
        if (err || res.error) {
          console.error('APIエラー詳細:', { err, resError: res?.error });
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.POST_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
            error: res?.error?.status || -1,
            data: { error: -1 },
          });
        } else {
          const error = res.body?.error || 0;
          console.log('サーバーレスポンスのerrorコード:', error);
          if (error === 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.POST_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
              data: res.body,
            });
          } else {
            // 認証エラー（error: 200など）の場合はログアウト処理を実行
            if (error === 200) {
              Utils.logoutIfneccessary(new Error('Authentication failed'));
            }
            AppDispatcher.dispatch({
              type: CCAPIEvents.POST_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
              data: { error },
            });
          }
        }
        if (cb) {
          cb(err, res)
        }
      });
  },

  // deleteRightPanelWidgetsSettings(id: string) {
  //   request
  //     .del(APIRoot.get() + `${url}/${id}`)
  //     .withCredentials()
  //     .type('form')
  //     .set({
  //       'X-CSRF-Token': antiCsrfToken,
  //       'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
  //     })
  //     .end((err, res) => {
  //       if (err || res.error) {
  //         Utils.logoutIfneccessary(err);
  //         AppDispatcher.dispatch({
  //           type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
  //           error: res?.error?.status || -1,
  //           data: { error: -1 },
  //         });
  //       } else {
  //         const error = res.body?.error || 0;
  //         if (error === 0) {
  //           AppDispatcher.dispatch({
  //             type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
  //             data: res.body,
  //           });
  //         } else {
  //           // 認証エラー（error: 200など）の場合はログアウト処理を実行
  //           if (error === 200) {
  //             Utils.logoutIfneccessary(new Error('Authentication failed'));
  //           }
  //           AppDispatcher.dispatch({
  //             type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
  //             data: { error },
  //           });
  //         }
  //       }
  //     });
  // },
};

export default RightPanelWidgetsAction;
