'use strict';

import { EventEmitter } from 'events';
import assign from 'object-assign';
import AppDispatcher from '../../../dispatcher/AppDispatcher';
import AppConstants from '../../../constants/AppConstants';
import CCAPIEvents from '../../../constants/CC_APIEvents';
import { RightPanelWidgetsAppDispatcherAction, RightPanelWidgetsSettingObj } from './types';

const StoreEvents = AppConstants.StoreEvents;
const CHANGE_EVENT = StoreEvents.CHANGE;

const MAX_WIDGETS = 5;

let rightPanelWidgetsSettingsData: RightPanelWidgetsSettingObj[] = [];

let errorCode: number = 0;
let successFlag: boolean = false;

function reorderWidgets() {
  rightPanelWidgetsSettingsData = rightPanelWidgetsSettingsData
    .filter(w => w.operation !== 'delete')
    .sort((a, b) => a.order - b.order)
    .map((widget, idx) => ({ ...widget, order: idx + 1 }));
}

export const RightPanelWidgetsSettings = assign({}, EventEmitter.prototype, {
  emitChange() {
    this.emit(CHANGE_EVENT);
  },
  addChangeListener(callback: () => void) {
    this.on(CHANGE_EVENT, callback);
  },
  removeChangeListener(callback: () => void) {
    this.removeListener(CHANGE_EVENT, callback);
  },
  initData() {
    rightPanelWidgetsSettingsData = [];
    errorCode = 0;
    successFlag = false;
  },
  data(): { widgets: RightPanelWidgetsSettingObj[]; error: number; success: boolean } {
    return {
      widgets: rightPanelWidgetsSettingsData,
      error: errorCode,
      success: successFlag,
    };
  },
});

RightPanelWidgetsSettings.dispatchToken = AppDispatcher.register((action: RightPanelWidgetsAppDispatcherAction) => {
  if (RightPanelWidgetsSettings.listeners(CHANGE_EVENT).length < 1) return false;
  if (!action.data) return false;

  switch (action.type) {
    case CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
      RightPanelWidgetsSettings.initData();

      if (Array.isArray(action.data.rightPanelWidgets) && action.data.rightPanelWidgets.length > 0) {
        rightPanelWidgetsSettingsData = action.data.rightPanelWidgets.map((widget: any) => ({
          id: widget.id || '',
          domainId: widget.domainId || '',
          order: widget.order || 1,
          title: widget.title || '',
          url: widget.url || '',
          iconType: widget.iconType || 'default',
          iconFileId: widget.iconFileId || '',
          updatedAt: widget.updatedAt || '',
          operation: 'update',
        }));
      } else {
        rightPanelWidgetsSettingsData = [];
      }

      errorCode = 0;
      successFlag = false; // GETの場合はsuccessフラグをfalseに（データ取得なので成功メッセージは不要）

      reorderWidgets();

      RightPanelWidgetsSettings.emitChange();
      break;

    case CCAPIEvents.POST_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
      errorCode = 0;
      successFlag = true;
      RightPanelWidgetsSettings.emitChange();
      break;

    case CCAPIEvents.POST_RIGHT_PANEL_WIDGETS_SETTINGS_NG:
      errorCode = action.data.error || -1;
      successFlag = false;
      RightPanelWidgetsSettings.emitChange();
      break;

    // case 'ADD_WIDGET': {
    //   const activeCount = rightPanelWidgetsSettingsData.filter(w => w.operation !== 'delete').length;
    //   if (activeCount >= MAX_WIDGETS) break;

    //   const newWidget: RightPanelWidgetsSettingObj = {
    //     id: 'temp_' + Date.now(),
    //     domainId: '',
    //     order: rightPanelWidgetsSettingsData.length + 1,
    //     title: '',
    //     url: '',
    //     iconType: 'default',
    //     iconFileId: '',
    //     updatedAt: '',
    //     operation: 'insert',
    //   };
    //   rightPanelWidgetsSettingsData.push(newWidget);
    //   reorderWidgets();
    //   RightPanelWidgetsSettings.emitChange();
    //   break;
    // }

    // case 'DELETE_WIDGET': {
    //   const index = rightPanelWidgetsSettingsData.findIndex(w => w.id === action.data.id);
    //   if (index === -1) break;

    //   if (rightPanelWidgetsSettingsData[index].operation === 'insert') {
    //     rightPanelWidgetsSettingsData.splice(index, 1);
    //   } else {
    //     rightPanelWidgetsSettingsData[index].operation = 'delete';
    //   }
    //   reorderWidgets();
    //   RightPanelWidgetsSettings.emitChange();
    //   break;
    // }

    // case 'MOVE_WIDGET_UP': {
    //   const index = rightPanelWidgetsSettingsData.findIndex(w => w.id === action.data.id);
    //   if (index <= 0) break; // 先頭は移動不可

    //   [rightPanelWidgetsSettingsData[index - 1], rightPanelWidgetsSettingsData[index]] =
    //     [rightPanelWidgetsSettingsData[index], rightPanelWidgetsSettingsData[index - 1]];

    //   reorderWidgets();
    //   RightPanelWidgetsSettings.emitChange();
    //   break;
    // }

    // case 'UPDATE_WIDGET': {
    //   const index = rightPanelWidgetsSettingsData.findIndex(w => w.id === action.data.id);
    //   if (index === -1) break;

    //   const updates = action.data as any; // 型の問題を回避
    //   rightPanelWidgetsSettingsData[index] = {
    //     ...rightPanelWidgetsSettingsData[index],
    //     title: updates.title !== undefined ? updates.title : rightPanelWidgetsSettingsData[index].title,
    //     url: updates.url !== undefined ? updates.url : rightPanelWidgetsSettingsData[index].url,
    //     iconType: updates.iconType !== undefined ? updates.iconType : rightPanelWidgetsSettingsData[index].iconType,
    //     iconFileId: updates.iconFileId !== undefined ? updates.iconFileId : rightPanelWidgetsSettingsData[index].iconFileId,
    //   };
      
    //   RightPanelWidgetsSettings.emitChange();
    //   break;
    // }

    default:
      break;
  }
});
